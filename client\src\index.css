@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Design Tokens */
@import './styles/design-tokens.css';

/* Import design system and utilities */
@import './styles/design-system.css';
@import './styles/widget-system.css';
@import './styles/optimizations.css';
@import './styles/navigation-layout.css';
@import './styles/vietnamese-design-system.css';
@import './styles/scroll-fix.css';

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }

  html {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply text-gray-900 dark:text-gray-100;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }

  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.25;
    letter-spacing: -0.025em;
  }

  p {
    line-height: 1.65;
  }

  /* Improved focus states */
  button:focus-visible,
  a:focus-visible {
    @apply outline-none ring-2 ring-blue-500/50 ring-offset-2 ring-offset-white dark:ring-offset-gray-900;
  }
}

@layer base {
  * {
    @apply border-secondary-200;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html, body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    overflow-x: hidden; /* Prevent horizontal scroll */
    height: 100%;
  }

  #root {
    overflow-x: hidden;
    min-height: 100vh;
    height: auto;
  }
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Legacy styles - to be removed gradually */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid #f3f4f6;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Chat Layout Improvements */
.chat-container {
  @apply h-full flex flex-col;
}

.chat-messages {
  @apply flex-1 overflow-y-auto;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 transparent;
}

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background-color: #94a3b8;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 0 0.5rem;
  }

  .btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
  }

  .chat-messages {
    padding: 0.75rem;
  }
}

/* Smooth transitions */
* {
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
}

/* Legacy component styles - moved to design-system.css */

/* Responsive Design */
@layer utilities {
  @media (max-width: 768px) {
    .sidebar {
      @apply -translate-x-full;
    }

    .sidebar-mobile-open {
      @apply translate-x-0;
    }

    .container {
      @apply px-2;
    }
  }
}

/* Animations moved to design-system.css */

/* Global Typography Improvements */
@layer base {
  html {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply text-gray-900 dark:text-gray-100;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }

  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.25;
    letter-spacing: -0.025em;
  }

  p {
    line-height: 1.65;
  }

  /* Improved focus states */
  button:focus-visible,
  a:focus-visible {
    @apply outline-none ring-2 ring-blue-500/50 ring-offset-2 ring-offset-white dark:ring-offset-gray-900;
  }
}

/* Enhanced Animation Classes */
@layer components {
  .card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .card-hover:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .floating-card {
    animation: float 6s ease-in-out infinite;
  }

  .stagger-1 {
    animation-delay: 0s;
  }

  .stagger-2 {
    animation-delay: 0.2s;
  }

  .stagger-3 {
    animation-delay: 0.4s;
  }

  .stagger-4 {
    animation-delay: 0.6s;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  /* Consistent gradient backgrounds */
  .gradient-primary {
    @apply bg-gradient-to-r from-blue-600 to-purple-600;
  }

  .gradient-secondary {
    @apply bg-gradient-to-r from-blue-500 to-cyan-500;
  }

  .gradient-success {
    @apply bg-gradient-to-r from-emerald-500 to-green-600;
  }

  .gradient-warning {
    @apply bg-gradient-to-r from-amber-500 to-orange-600;
  }

  /* Improved shadows */
  .shadow-soft {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
  }

  .shadow-soft-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  /* Accessibility improvements */
  .skip-link {
    @apply absolute left-0 top-0 bg-blue-600 text-white px-4 py-2 z-50 transform -translate-y-full focus:translate-y-0 transition-transform;
  }
}

/* Dark mode improvements */
@layer utilities {
  .dark .shadow-soft {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
  }

  .dark .shadow-soft-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  }
}

/* Scrollbar styling */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.7);
  }
}

/* Performance optimizations */
* {
  box-sizing: border-box;
}

img {
  max-width: 100%;
  height: auto;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .floating-card {
    animation: none;
  }
}

/* Chat page specific styles for navigation layout integration */
.chat-page-active {
  overflow: hidden;
}

/* Ensure chat container works well with navigation layout */
.nav-content.nav-container .chat-page-active {
  height: 100vh;
}

/* Mobile adjustments for chat with navigation */
@media (max-width: 768px) {
  .chat-page-active .messenger-layout {
    padding-bottom: 60px; /* Account for mobile tab bar */
  }
}
