/* Navigation Layout Optimizations */

/* Fix double scroll issue - Remove height constraints from container */
.nav-container {
  position: relative;
  overflow-x: hidden !important;
  overflow-y: auto !important;
  height: 400px !important;
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}


.nav-container-overflow {
  position: relative;
  overflow-x: hidden !important;
  overflow-y: auto !important;
  max-height: calc(100vh - 69px - 90px - 193px - 82px) !important;
}

.nav-container.with-sidebar {
  margin-left: 0;
}

@media (min-width: 768px) {
  .nav-container.with-sidebar {
    margin-left: 20rem; /* 320px / 16 = 20rem */
  }
}

/* Ensure sidebar doesn't interfere with content */
.nav-sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 30;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.nav-sidebar-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Enhanced responsive hamburger menu positioning */
.hamburger-menu {
  position: fixed !important;
  z-index: 51 !important;
  visibility: visible !important;
  display: block !important;
}

/* Responsive positioning - let JS handle top/left for auto-scaling */
@media (max-width: 374px) {
  .hamburger-menu {
    top: 0.75rem !important;
    left: 0.75rem !important;
  }
}

@media (min-width: 375px) and (max-width: 767px) {
  .hamburger-menu {
    top: 1rem !important;
    left: 1rem !important;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .hamburger-menu {
    top: 1rem !important;
    left: 1rem !important;
  }
}

@media (min-width: 1024px) {
  .hamburger-menu {
    top: 1.25rem !important;
    left: 1.25rem !important;
  }
}

.nav-sidebar {
  z-index: 40 !important;
}

/* Smooth transitions for all navigation elements */
.nav-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hide sidebar completely when closed */
.nav-sidebar.closed {
  transform: translateX(-100%);
  visibility: hidden;
}

.nav-sidebar.open {
  transform: translateX(0);
  visibility: visible;
}

/* Enhanced mobile specific optimizations */
@media (max-width: 767px) {
  .nav-container.with-sidebar {
    margin-left: 0 !important;
  }
  
  .nav-sidebar {
    width: 100vw !important;
    max-width: 320px !important;
  }
}

/* Enhanced tablet optimizations */
@media (min-width: 768px) and (max-width: 1023px) {
  .nav-container.with-sidebar {
    margin-left: 0 !important;
  }
  
  .nav-sidebar {
    width: 85vw !important;
    max-width: 380px !important;
  }
}

/* Desktop optimizations */
@media (min-width: 1024px) {
  .nav-sidebar {
    width: 320px !important;
    max-width: 400px !important;
  }
}

/* Ensure content doesn't jump and fix scroll issues */
.nav-content {
  width: 100%;
  position: relative;
  overflow-y: visible;
  height: auto;
}

/* Enhanced responsive hamburger menu sizing and positioning */
.nav-hamburger {
  position: fixed !important;
  z-index: 51 !important;
}

/* Responsive sizing and positioning for nav-hamburger */
@media (max-width: 374px) {
  .nav-hamburger {
    top: 0.75rem !important;
    left: 0.75rem !important;
    width: 2.75rem !important;
    height: 2.75rem !important;
  }
}

@media (min-width: 375px) and (max-width: 767px) {
  .nav-hamburger {
    top: 1rem !important;
    left: 1rem !important;
    width: 3rem !important;
    height: 3rem !important;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .nav-hamburger {
    top: 1rem !important;
    left: 1rem !important;
    width: 3.5rem !important;
    height: 3.5rem !important;
  }
}

@media (min-width: 1024px) {
  .nav-hamburger {
    top: 1.25rem !important;
    left: 1.25rem !important;
    width: 4rem !important;
    height: 4rem !important;
  }
}

/* Responsive sidebar adjustments */
@media (max-width: 374px) {
  .nav-sidebar {
    width: 100vw !important;
    max-width: 280px !important;
  }
}

/* Enhanced responsive animations and transitions */
@media (prefers-reduced-motion: reduce) {
  .hamburger-menu,
  .nav-hamburger,
  .nav-sidebar {
    transition: none !important;
    animation: none !important;
  }
}

/* Touch-friendly enhancements for mobile */
@media (max-width: 767px) {
  .hamburger-menu,
  .nav-hamburger {
    min-height: 44px !important;
    min-width: 44px !important;
  }
}
