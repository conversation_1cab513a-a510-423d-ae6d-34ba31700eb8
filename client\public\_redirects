# Render Redirects for FactCheck App

# Redirect all API calls to API Gateway (backend services only)
# Note: These URLs will be replaced during build process with actual environment variables
/api/* https://backup-zhhs.onrender.com/api/:splat 200
/auth/* https://backup-zhhs.onrender.com/auth/:splat 200
/users/* https://backup-zhhs.onrender.com/users/:splat 200

# These routes are frontend routes and should be handled by SPA fallback, not backend redirects
# /chat/* https://backup-zhhs.onrender.com/chat/:splat 200
# /community/* https://backup-zhhs.onrender.com/community/:splat 200

# API routes only (backend services)
/news/* https://backup-zhhs.onrender.com/news/:splat 200
/links/* https://backup-zhhs.onrender.com/links/:splat 200
/admin/* https://backup-zhhs.onrender.com/admin/:splat 200
/posts/* https://backup-zhhs.onrender.com/posts/:splat 200
/votes/* https://backup-zhhs.onrender.com/votes/:splat 200
/comments/* https://factcheck-api-gateway.onrender.com/comments/:splat 200

# SPA fallback - all other routes (including /community, /chat) serve index.html
/* /index.html 200
