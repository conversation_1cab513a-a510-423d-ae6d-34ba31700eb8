/* Enhanced Messenger Layout - Unified with Navigation */
.messenger-layout {
  @apply flex flex-col h-full bg-gray-50 dark:bg-gray-900;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Remove chat-fullscreen class since we're no longer full-screen */
.messenger-layout.dark {
  color-scheme: dark;
}

/* Main messenger content container */
.messenger-content {
  @apply flex flex-1 h-full overflow-hidden;
}

/* Chat sidebar styles - compatible with navigation layout */
.messenger-sidebar {
  @apply w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col transition-all duration-300;
  flex-shrink: 0;
}

.messenger-sidebar.collapsed {
  @apply w-20;
}

.messenger-sidebar.open {
  @apply flex;
}

@media (max-width: 1024px) {
  .messenger-sidebar {
    @apply fixed left-0 top-0 h-full z-30 transform -translate-x-full;
    width: 320px;
  }
  
  .messenger-sidebar.open {
    @apply translate-x-0;
  }
}

/* Chat area styles */
.messenger-chat-area {
  @apply flex-1 flex flex-col bg-gray-50 dark:bg-gray-900 overflow-hidden;
  min-width: 0; /* Prevent flex item from overflowing */
}

/* Messages area - improved scrolling */
.messenger-messages-area {
  @apply flex-1 overflow-y-auto;
  scroll-behavior: smooth;
}

.messenger-bubble-container {
  @apply p-4 space-y-4;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

/* Message styling */
.message-container {
  @apply flex items-end space-x-2 max-w-[85%];
}

.message-container.user {
  @apply flex-row-reverse space-x-reverse ml-auto;
}

.message-container.bot {
  @apply mr-auto;
}

.message-avatar {
  @apply w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-semibold flex-shrink-0;
}

.message-content {
  @apply flex flex-col space-y-1;
}

.message-bubble {
  @apply px-4 py-2 rounded-2xl shadow-sm;
  max-width: fit-content;
  word-wrap: break-word;
}

.message-bubble.user {
  @apply bg-blue-500 text-white ml-auto;
  border-bottom-right-radius: 0.5rem;
}

.message-bubble.bot {
  @apply bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 border border-gray-200 dark:border-gray-600 mr-auto;
  border-bottom-left-radius: 0.5rem;
}

.message-timestamp {
  @apply text-xs text-gray-500 dark:text-gray-400 flex items-center space-x-1;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.message-container.user .message-timestamp {
  @apply justify-end;
}

/* Typing indicator */
.typing-indicator {
  @apply flex items-end space-x-2 max-w-[85%];
}

.typing-dots {
  @apply bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 px-4 py-3 rounded-2xl flex space-x-1;
  border-bottom-left-radius: 0.5rem;
}

.typing-dot {
  @apply w-2 h-2 bg-gray-400 rounded-full;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

/* Input container - messenger style */
.messenger-input-container {
  @apply flex items-end space-x-3 p-4 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700;
  flex-shrink: 0;
}

.messenger-input-box {
  @apply flex-1 px-4 py-3 bg-gray-100 dark:bg-gray-700 rounded-2xl border-0 resize-none text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400;
  outline: none;
  max-height: 120px;
  min-height: 44px;
  line-height: 1.4;
}

.messenger-input-box:focus {
  @apply ring-2 ring-blue-500 bg-white dark:bg-gray-600;
}

.messenger-attachment-button {
  @apply p-2 text-gray-500 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-all duration-200;
  flex-shrink: 0;
}

.messenger-send-button {
  @apply p-2 bg-blue-500 hover:bg-blue-600 text-white rounded-full transition-all duration-200 flex items-center justify-center;
  flex-shrink: 0;
  width: 36px;
  height: 36px;
}

/* Search input styling */
.messenger-search-input {
  transition: all 0.2s ease;
}

.messenger-search-input:focus {
  @apply bg-white dark:bg-gray-600;
}

/* Enhanced responsive design */
@media (max-width: 768px) {
  .messenger-sidebar {
    width: 100vw;
  }
  
  .message-container {
    @apply max-w-[95%];
  }
  
  .messenger-input-container {
    @apply p-3 space-x-2;
  }
}

/* Accessibility improvements */
.messenger-layout *:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.messenger-layout button:focus-visible {
  @apply ring-2 ring-blue-500 ring-offset-2;
}

/* Dark mode improvements */
@media (prefers-color-scheme: dark) {
  .messenger-layout {
    color-scheme: dark;
  }
}

/* Animation improvements */
.messenger-sidebar,
.messenger-chat-area {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Performance optimizations */
.messenger-messages-area {
  contain: layout style paint;
  will-change: scroll-position;
}

.message-bubble {
  contain: layout style paint;
}
