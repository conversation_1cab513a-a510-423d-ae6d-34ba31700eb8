<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anti-Fraud Platform - Simple Monitoring Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.1em;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 3em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-size: 1.1em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .healthy { color: #4CAF50; }
        .unhealthy { color: #FF9800; }
        .offline { color: #F44336; }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .service-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .service-card:hover {
            transform: translateY(-3px);
        }
        
        .service-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .service-name {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
        }
        
        .service-status {
            padding: 5px 15px;
            border-radius: 20px;
            color: white;
            font-size: 0.9em;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-healthy { background: #4CAF50; }
        .status-unhealthy { background: #FF9800; }
        .status-offline { background: #F44336; }
        
        .service-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 0.9em;
            color: #666;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 50px;
            padding: 15px 25px;
            font-size: 1.1em;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
            font-size: 1.2em;
        }
        
        .error {
            background: #ffebee;
            color: #c62828;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        
        .last-update {
            text-align: center;
            color: #666;
            font-size: 0.9em;
            margin-top: 20px;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .refreshing {
            animation: pulse 1s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ Anti-Fraud Platform</h1>
            <p>Simple Local Monitoring Dashboard - No Docker Required</p>
        </div>
        
        <div class="stats" id="stats">
            <div class="stat-card">
                <div class="stat-number healthy" id="healthy-count">-</div>
                <div class="stat-label">Healthy Services</div>
            </div>
            <div class="stat-card">
                <div class="stat-number unhealthy" id="unhealthy-count">-</div>
                <div class="stat-label">Unhealthy Services</div>
            </div>
            <div class="stat-card">
                <div class="stat-number offline" id="offline-count">-</div>
                <div class="stat-label">Offline Services</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="total-count">-</div>
                <div class="stat-label">Total Services</div>
            </div>
        </div>
        
        <div class="services-grid" id="services">
            <div class="loading">Loading services...</div>
        </div>
        
        <div class="last-update" id="last-update"></div>
    </div>
    
    <button class="refresh-btn" onclick="refreshData()">🔄 Refresh</button>
    
    <script>
        let refreshInterval;
        
        async function fetchData() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                return data;
            } catch (error) {
                console.error('Error fetching data:', error);
                return null;
            }
        }
        
        function updateStats(data) {
            const summary = data.summary;
            document.getElementById('healthy-count').textContent = summary.healthy;
            document.getElementById('unhealthy-count').textContent = summary.unhealthy;
            document.getElementById('offline-count').textContent = summary.offline;
            document.getElementById('total-count').textContent = summary.total;
        }
        
        function formatUptime(seconds) {
            if (!seconds) return 'N/A';
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = Math.floor(seconds % 60);
            return `${hours}h ${minutes}m ${secs}s`;
        }
        
        function formatMemory(memory) {
            if (!memory) return 'N/A';
            if (typeof memory === 'string') return memory;
            if (memory.used) return memory.used;
            return 'N/A';
        }
        
        function renderServices(services) {
            const container = document.getElementById('services');
            
            if (!services || services.length === 0) {
                container.innerHTML = '<div class="error">No services found</div>';
                return;
            }
            
            container.innerHTML = services.map(service => `
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-name">${service.name}</div>
                        <div class="service-status status-${service.status}">${service.status}</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-item">
                            <span>Port:</span>
                            <span>${service.port}</span>
                        </div>
                        <div class="detail-item">
                            <span>Response Time:</span>
                            <span>${service.responseTime}</span>
                        </div>
                        <div class="detail-item">
                            <span>Uptime:</span>
                            <span>${formatUptime(service.response?.uptime)}</span>
                        </div>
                        <div class="detail-item">
                            <span>Memory:</span>
                            <span>${formatMemory(service.response?.memory)}</span>
                        </div>
                        <div class="detail-item">
                            <span>Service:</span>
                            <span>${service.response?.service || service.name}</span>
                        </div>
                        <div class="detail-item">
                            <span>Last Check:</span>
                            <span>${new Date().toLocaleTimeString()}</span>
                        </div>
                    </div>
                    ${service.error ? `<div class="error">Error: ${service.error}</div>` : ''}
                </div>
            `).join('');
        }
        
        async function refreshData() {
            const refreshBtn = document.querySelector('.refresh-btn');
            refreshBtn.classList.add('refreshing');
            
            const data = await fetchData();
            
            if (data) {
                updateStats(data);
                renderServices(data.services);
                document.getElementById('last-update').textContent = 
                    `Last updated: ${new Date(data.timestamp).toLocaleString()}`;
            } else {
                document.getElementById('services').innerHTML = 
                    '<div class="error">Failed to fetch data. Please check if the monitoring service is running.</div>';
            }
            
            refreshBtn.classList.remove('refreshing');
        }
        
        // Initial load
        refreshData();
        
        // Auto refresh every 30 seconds
        refreshInterval = setInterval(refreshData, 30000);
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        });
    </script>
</body>
</html>
