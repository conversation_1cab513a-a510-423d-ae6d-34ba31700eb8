/* Enhanced Chat Theme Integration with Design System */

/* CSS Variables for consistent theming */
:root {
  --messenger-primary: #0084ff;
  --messenger-primary-hover: #0066cc;
  --messenger-secondary: #f1f3f4;
  --messenger-text: #1c1e21;
  --messenger-text-light: #65676b;
  --messenger-border: rgba(0, 0, 0, 0.1);
  --messenger-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  --messenger-radius: 20px;
  --messenger-spacing: 16px;
}

.dark {
  --messenger-primary: #0084ff;
  --messenger-primary-hover: #0066cc;
  --messenger-secondary: rgba(55, 65, 81, 0.95);
  --messenger-text: #f9fafb;
  --messenger-text-light: #9ca3af;
  --messenger-border: rgba(255, 255, 255, 0.1);
  --messenger-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
}

/* Override chat-specific styles to use design tokens */
.chat-fullscreen {
  background: hsl(var(--background, 0 0% 100%)) !important;
  color: hsl(var(--foreground, 224 71.4% 4.1%)) !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

.chat-fullscreen.dark {
  background: hsl(var(--background, 222.2 84% 4.9%)) !important;
  color: hsl(var(--foreground, 210 40% 98%)) !important;
}

/* Enhanced Header styling with design system */
.messenger-header {
  background: hsl(var(--card, 0 0% 100%)) !important;
  border-bottom: 1px solid hsl(var(--border, 220 13% 91%)) !important;
  box-shadow: var(--shadow-sm, 0 1px 2px 0 rgb(0 0 0 / 0.05)) !important;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
}

.dark .messenger-header {
  background: hsl(var(--card, 222.2 84% 4.9%)) !important;
  border-bottom: 1px solid hsl(var(--border, 217.2 32.6% 17.5%)) !important;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.3) !important;
}

/* Enhanced Sidebar styling */
.messenger-sidebar {
  background: hsl(var(--card, 0 0% 100%)) !important;
  border-right: 1px solid hsl(var(--border, 220 13% 91%)) !important;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
}

.dark .messenger-sidebar {
  background: hsl(var(--card, 222.2 84% 4.9%)) !important;
  border-right: 1px solid hsl(var(--border, 217.2 32.6% 17.5%)) !important;
}

/* Enhanced Chat area styling */
.messenger-chat-area {
  background: linear-gradient(135deg, 
    hsl(var(--background, 0 0% 98%)) 0%, 
    hsl(var(--muted, 210 40% 96%)) 100%) !important;
}

.dark .messenger-chat-area {
  background: linear-gradient(135deg, 
    hsl(var(--background, 222.2 84% 4.9%)) 0%, 
    hsl(var(--muted, 217.2 32.6% 17.5%)) 100%) !important;
}

/* Enhanced Messages area */
.messenger-messages-area {
  background: transparent !important;
}

.dark .messenger-messages-area {
  background: transparent !important;
}

/* Enhanced Message bubbles with design system colors */
.message-bubble.user {
  background: linear-gradient(135deg, var(--messenger-primary) 0%, var(--messenger-primary-hover) 100%) !important;
  color: white !important;
  box-shadow: var(--messenger-shadow) !important;
  border: none !important;
  
  /* Enhanced text display - prevent auto line breaks */
  word-wrap: break-word !important;
  word-break: break-word !important;
  overflow-wrap: break-word !important;
  white-space: pre-wrap !important;
  hyphens: auto !important;
  -webkit-hyphens: auto !important;
  
  /* Better width control - Responsive design */
  width: fit-content !important;
  min-width: 60px !important;
  max-width: 85% ; /* Increased from 75% to reduce unwanted line breaks */
  
  /* Ensure proper text flow */
  line-height: 1.5 !important;
  padding: 12px 16px !important;
  border-radius: 20px !important;
  border-bottom-right-radius: 6px !important;
}

.message-bubble.bot {
  background: var(--messenger-secondary) !important;
  color: var(--messenger-text) !important;
  border: 1px solid var(--messenger-border) !important;
  box-shadow: var(--messenger-shadow) !important;
  
  /* Enhanced text display - prevent auto line breaks */
  word-wrap: break-word !important;
  word-break: break-word !important;
  overflow-wrap: break-word !important;
  white-space: pre-wrap !important;
  hyphens: auto !important;
  -webkit-hyphens: auto !important;
  
  /* Better width control - Responsive design */
  width: fit-content !important;
  min-width: 60px !important;
  max-width: 85% !important; /* Increased from 75% to reduce unwanted line breaks */
  
  /* Ensure proper text flow */
  line-height: 1.5 !important;
  padding: 12px 16px !important;
  border-radius: 20px !important;
  border-bottom-left-radius: 6px !important;
}

.dark .message-bubble.bot {
  background: var(--messenger-secondary) !important;
  color: var(--messenger-text) !important;
  border: 1px solid var(--messenger-border) !important;
  box-shadow: var(--messenger-shadow) !important;
}

/* Enhanced Message containers for better layout */
.message-container {
  display: flex !important;
  align-items: flex-start !important;
  gap: 8px !important;
  margin-bottom: 8px !important;
  width: 100% !important;
  max-width: 100% !important;
}

.message-container.user {
  justify-content: flex-start !important;
}

.message-container.bot {
  flex-direction: row !important;
  justify-content: flex-start !important;
}

.message-content {
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
  flex: 1 !important;
  min-width: 0 !important;
  max-width: calc(100% - 48px) !important;
}

/* Enhanced Input container */
.messenger-input-container {
  background: hsl(var(--card, 0 0% 100%)) !important;
  border-top: 1px solid hsl(var(--border, 220 13% 91%)) !important;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
}

.dark .messenger-input-container {
  background: hsl(var(--card, 222.2 84% 4.9%)) !important;
  border-top: 1px solid hsl(var(--border, 217.2 32.6% 17.5%)) !important;
}

/* Enhanced Input box */
.messenger-input-box {
  background: hsl(var(--muted, 210 40% 96%)) !important;
  border: 2px solid transparent !important;
  color: hsl(var(--foreground, 224 71.4% 4.1%)) !important;
  border-radius: var(--radius-full, 9999px) !important;
  font-family: inherit !important;
}

.messenger-input-box:focus {
  border-color: var(--messenger-primary) !important;
  background: hsl(var(--background, 0 0% 100%)) !important;
  box-shadow: 0 0 0 4px rgba(0, 132, 255, 0.1) !important;
  transform: translateY(-1px) !important;
}

.dark .messenger-input-box {
  background: hsl(var(--muted, 217.2 32.6% 17.5%)) !important;
  color: hsl(var(--foreground, 210 40% 98%)) !important;
  border-color: transparent !important;
}

.dark .messenger-input-box:focus {
  background: hsl(var(--background, 222.2 84% 4.9%)) !important;
  border-color: var(--messenger-primary) !important;
  box-shadow: 0 0 0 4px rgba(0, 132, 255, 0.2) !important;
}

/* Enhanced Buttons */
.messenger-send-button,
.messenger-attachment-button {
  border-radius: var(--radius-full, 50%) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border: none !important;
  cursor: pointer !important;
}

.messenger-send-button {
  background: linear-gradient(135deg, var(--messenger-primary) 0%, var(--messenger-primary-hover) 100%) !important;
  color: white !important;
  box-shadow: 0 2px 12px rgba(0, 132, 255, 0.3) !important;
}

.messenger-send-button:hover {
  box-shadow: 0 4px 20px rgba(0, 132, 255, 0.4) !important;
  transform: scale(1.05) !important;
}

.messenger-attachment-button {
  background: hsl(var(--muted, 210 40% 96%)) !important;
  color: hsl(var(--muted-foreground, 215.4 16.3% 46.9%)) !important;
}

.messenger-attachment-button:hover {
  background: hsl(var(--accent, 210 40% 94%)) !important;
  transform: scale(1.05) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.dark .messenger-attachment-button {
  background: hsl(var(--muted, 217.2 32.6% 17.5%)) !important;
  color: hsl(var(--muted-foreground, 215 20.2% 65.1%)) !important;
}

.dark .messenger-attachment-button:hover {
  background: hsl(var(--accent, 216 34% 17%)) !important;
}

/* Enhanced Conversation items */
.conversation-item {
  border-radius: var(--radius-lg, 0.75rem) !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  margin: 2px 8px !important;
}

.conversation-item:hover {
  background: hsl(var(--accent, 210 40% 94%)) !important;
  transform: translateX(4px) !important;
}

.conversation-item.active {
  background: rgba(0, 132, 255, 0.1) !important;
  border-left: 4px solid var(--messenger-primary) !important;
}

.dark .conversation-item:hover {
  background: hsl(var(--accent, 216 34% 17%)) !important;
}

.dark .conversation-item.active {
  background: rgba(0, 132, 255, 0.2) !important;
}

/* Enhanced Search input */
.messenger-search-input {
  background: hsl(var(--muted, 210 40% 96%)) !important;
  border: 1px solid transparent !important;
  color: hsl(var(--foreground, 224 71.4% 4.1%)) !important;
  border-radius: var(--radius-full, 9999px) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.messenger-search-input:focus {
  border-color: var(--messenger-primary) !important;
  background: hsl(var(--background, 0 0% 100%)) !important;
  box-shadow: 0 0 0 2px rgba(0, 132, 255, 0.1) !important;
  transform: translateY(-1px) !important;
}

.dark .messenger-search-input {
  background: hsl(var(--muted, 217.2 32.6% 17.5%)) !important;
  color: hsl(var(--foreground, 210 40% 98%)) !important;
}

.dark .messenger-search-input:focus {
  background: hsl(var(--background, 222.2 84% 4.9%)) !important;
  border-color: var(--messenger-primary) !important;
  box-shadow: 0 0 0 2px rgba(0, 132, 255, 0.2) !important;
}

/* Enhanced Welcome screen */
.welcome-screen {
  background: hsl(var(--background, 0 0% 100%)) !important;
  color: hsl(var(--foreground, 224 71.4% 4.1%)) !important;
}

.welcome-screen .feature-card {
  background: hsl(var(--card, 0 0% 100%)) !important;
  border: 1px solid hsl(var(--border, 220 13% 91%)) !important;
  border-radius: var(--radius-lg, 0.75rem) !important;
  box-shadow: var(--shadow-sm, 0 1px 2px 0 rgb(0 0 0 / 0.05)) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.welcome-screen .feature-card:hover {
  box-shadow: var(--shadow-md, 0 4px 6px -1px rgb(0 0 0 / 0.1)) !important;
  transform: translateY(-2px) !important;
}

.dark .welcome-screen {
  background: hsl(var(--background, 222.2 84% 4.9%)) !important;
  color: hsl(var(--foreground, 210 40% 98%)) !important;
}

.dark .welcome-screen .feature-card {
  background: hsl(var(--card, 222.2 84% 4.9%)) !important;
  border: 1px solid hsl(var(--border, 217.2 32.6% 17.5%)) !important;
}

/* Enhanced Scrollbar styling */
.messenger-messages-area::-webkit-scrollbar {
  width: 6px;
}

.messenger-messages-area::-webkit-scrollbar-track {
  background: transparent;
}

.messenger-messages-area::-webkit-scrollbar-thumb {
  background: hsl(var(--border, 220 13% 91%));
  border-radius: 3px;
  transition: background 0.2s ease;
}

.messenger-messages-area::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground, 215.4 16.3% 46.9%));
}

.dark .messenger-messages-area::-webkit-scrollbar-thumb {
  background: hsl(var(--border, 217.2 32.6% 17.5%));
}

.dark .messenger-messages-area::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground, 215 20.2% 65.1%));
}

/* Enhanced Typing indicator */
.typing-indicator-modern {
  background: var(--messenger-secondary) !important;
  color: var(--messenger-text) !important;
  border-radius: var(--messenger-radius, 20px) !important;
  padding: 12px 16px !important;
  border: 1px solid var(--messenger-border) !important;
  box-shadow: var(--messenger-shadow) !important;
}

.dark .typing-indicator-modern {
  background: var(--messenger-secondary) !important;
  color: var(--messenger-text) !important;
}

/* Enhanced Reaction buttons */
.reaction-popup {
  background: hsl(var(--card, 0 0% 100%)) !important;
  border: 1px solid hsl(var(--border, 220 13% 91%)) !important;
  border-radius: 25px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  backdrop-filter: blur(20px) !important;
}

.dark .reaction-popup {
  background: hsl(var(--card, 222.2 84% 4.9%)) !important;
  border: 1px solid hsl(var(--border, 217.2 32.6% 17.5%)) !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4) !important;
}

.message-reactions button {
  background: rgba(0, 132, 255, 0.1) !important;
  border: 1px solid rgba(0, 132, 255, 0.2) !important;
  border-radius: var(--radius-full, 9999px) !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  color: var(--messenger-primary) !important;
}

.message-reactions button:hover {
  background: rgba(0, 132, 255, 0.2) !important;
  transform: scale(1.05) !important;
  box-shadow: 0 2px 8px rgba(0, 132, 255, 0.2) !important;
}

.dark .message-reactions button {
  background: rgba(0, 132, 255, 0.2) !important;
  border: 1px solid rgba(0, 132, 255, 0.3) !important;
}

.dark .message-reactions button:hover {
  background: rgba(0, 132, 255, 0.3) !important;
}

/* Status indicators */
.status-indicator.online {
  background: #10b981 !important;
  box-shadow: 0 0 0 2px white, 0 0 8px rgba(16, 185, 129, 0.3) !important;
}

.status-indicator.away {
  background: #f59e0b !important;
  box-shadow: 0 0 0 2px white, 0 0 8px rgba(245, 158, 11, 0.3) !important;
}

.status-indicator.offline {
  background: #6b7280 !important;
  box-shadow: 0 0 0 2px white !important;
}

.dark .status-indicator.online {
  box-shadow: 0 0 0 2px hsl(var(--card)), 0 0 8px rgba(16, 185, 129, 0.3) !important;
}

.dark .status-indicator.away {
  box-shadow: 0 0 0 2px hsl(var(--card)), 0 0 8px rgba(245, 158, 11, 0.3) !important;
}

.dark .status-indicator.offline {
  box-shadow: 0 0 0 2px hsl(var(--card)) !important;
}

/* Enhanced Mobile responsive adjustments */
@media (max-width: 768px) {
  .messenger-header {
    padding: 0 var(--spacing-md, 1rem) !important;
    height: 56px !important;
  }

  .messenger-input-container {
    padding: var(--spacing-md, 1rem) !important;
  }

  .messenger-messages-area {
    padding: var(--spacing-md, 1rem) !important;
  }

  .message-bubble.user,
  .message-bubble.bot {
    max-width: 90% !important; /* Increased for better mobile experience */
    font-size: 14px !important;
    padding: 10px 14px !important;
  }
}

/* Enhanced Tablet responsive adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
  .message-bubble.user,
  .message-bubble.bot {
    max-width: 85% ; /* Optimal width for tablet */
  }
}

/* Enhanced Desktop optimizations */
@media (min-width: 1025px) {
  .messenger-header {
    padding: 0 var(--spacing-xl, 2rem) !important;
    height: 64px !important;
  }

  .messenger-input-container {
    padding: 12px 90px  12px 16px !important;
  }

  .messenger-messages-area {
    padding: var(--spacing-xl, 2rem) !important;
  }

  .message-bubble.user,
  .message-bubble.bot {
    max-width: 80% ; /* Increased from 70% for better readability */
    font-size: 15px !important;
    padding: 12px 16px !important;
  }

  .message-bubble:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  }

  .dark .message-bubble:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4) !important;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .message-bubble.user {
    background: #0066cc !important;
    border: 2px solid #004499 !important;
  }

  .message-bubble.bot {
    background: white !important;
    border: 2px solid #333 !important;
    color: #000 !important;
  }

  .messenger-input-box {
    border: 2px solid #333 !important;
  }
}

/* Focus indicators for better accessibility */
.messenger-input-box:focus,
.messenger-search-input:focus,
button:focus-visible {
  outline: 2px solid var(--messenger-primary) !important;
  outline-offset: 2px !important;
} 