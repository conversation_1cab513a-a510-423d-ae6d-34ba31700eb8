<?xml version="1.0" encoding="UTF-8"?>
<svg width="900" height="420" viewBox="0 0 900 420" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect x="40" y="40" width="180" height="60" rx="12" fill="#F3F4F6" stroke="#111" stroke-width="2"/>
  <text x="130" y="75" text-anchor="middle" font-size="18" font-family="Arial" fill="#111">CLI Demo</text>

  <rect x="320" y="40" width="200" height="60" rx="12" fill="#E0F2FE" stroke="#0284C7" stroke-width="2"/>
  <text x="420" y="75" text-anchor="middle" font-size="18" font-family="Arial" fill="#0284C7">EventBus</text>

  <rect x="320" y="160" width="200" height="60" rx="12" fill="#FEF9C3" stroke="#CA8A04" stroke-width="2"/>
  <text x="420" y="195" text-anchor="middle" font-size="18" font-family="Arial" fill="#CA8A04">KurrentDB Event Store</text>

  <rect x="320" y="280" width="200" height="60" rx="12" fill="#DCFCE7" stroke="#16A34A" stroke-width="2"/>
  <text x="420" y="315" text-anchor="middle" font-size="18" font-family="Arial" fill="#16A34A">Materialized View</text>

  <rect x="680" y="160" width="180" height="60" rx="12" fill="#F3F4F6" stroke="#111" stroke-width="2"/>
  <text x="770" y="195" text-anchor="middle" font-size="18" font-family="Arial" fill="#111">Người dùng</text>

  <!-- Arrow: CLI -> EventBus -->
  <line x1="220" y1="70" x2="320" y2="70" stroke="#111" stroke-width="2" marker-end="url(#arrow)"/>
  <text x="270" y="60" text-anchor="middle" font-size="14" font-family="Arial" fill="#111">Gửi lệnh</text>

  <!-- Arrow: EventBus -> KurrentDB -->
  <line x1="420" y1="100" x2="420" y2="160" stroke="#0284C7" stroke-width="2" marker-end="url(#arrow-blue)"/>
  <text x="430" y="135" font-size="14" font-family="Arial" fill="#0284C7">Lưu event</text>

  <!-- Arrow: KurrentDB -> Materialized View -->
  <line x1="420" y1="220" x2="420" y2="280" stroke="#CA8A04" stroke-width="2" marker-end="url(#arrow-yellow)"/>
  <text x="430" y="255" font-size="14" font-family="Arial" fill="#CA8A04">Build trạng thái</text>

  <!-- Arrow: Materialized View -> Người dùng -->
  <line x1="520" y1="310" x2="680" y2="190" stroke="#16A34A" stroke-width="2" marker-end="url(#arrow-green)"/>
  <text x="600" y="250" font-size="14" font-family="Arial" fill="#16A34A">Query trạng thái</text>

  <!-- Arrow: CLI -> KurrentDB (Xem log, replay) -->
  <line x1="130" y1="100" x2="130" y2="190" stroke="#111" stroke-width="2"/>
  <line x1="130" y1="190" x2="320" y2="190" stroke="#111" stroke-width="2" marker-end="url(#arrow)"/>
  <text x="180" y="180" font-size="14" font-family="Arial" fill="#111">Xem log, replay</text>

  <defs>
    <marker id="arrow" markerWidth="10" markerHeight="10" refX="8" refY="5" orient="auto" markerUnits="strokeWidth">
      <path d="M0,2 L8,5 L0,8" fill="#111"/>
    </marker>
    <marker id="arrow-blue" markerWidth="10" markerHeight="10" refX="8" refY="5" orient="auto" markerUnits="strokeWidth">
      <path d="M0,2 L8,5 L0,8" fill="#0284C7"/>
    </marker>
    <marker id="arrow-yellow" markerWidth="10" markerHeight="10" refX="8" refY="5" orient="auto" markerUnits="strokeWidth">
      <path d="M0,2 L8,5 L0,8" fill="#CA8A04"/>
    </marker>
    <marker id="arrow-green" markerWidth="10" markerHeight="10" refX="8" refY="5" orient="auto" markerUnits="strokeWidth">
      <path d="M0,2 L8,5 L0,8" fill="#16A34A"/>
    </marker>
  </defs>
</svg> 