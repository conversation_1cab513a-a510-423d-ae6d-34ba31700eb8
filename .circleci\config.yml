version: 2.1

executors:
  node-executor:
    docker:
      - image: cimg/node:20.18
    environment:
      PUPPETEER_SKIP_CHROMIUM_DOWNLOAD: "true"
      NODE_ENV: "test"
    working_directory: ~/repo

commands:
  install_deps:
    description: "Install dependencies"
    parameters:
      path:
        type: string
    steps:
      - checkout
      - run: # step to install a browser
          name: Install Chromium
          command: |
            sudo apt-get update && sudo apt-get install -y chromium-browser
      - restore_cache:
          keys:
            - v1-dependencies-{{ checksum "<< parameters.path >>/package-lock.json" }}
            - v1-dependencies-
      - run:
          name: Install dependencies
          command: |
            cd << parameters.path >>
            if [ -f package-lock.json ]; then
              npm ci --loglevel verbose || npm install --loglevel verbose
            else
              npm install --loglevel verbose
            fi
            # Install shared dependencies if needed
            if [ -f "../../shared/package.json" ]; then
              cd ../../shared
              npm install --loglevel verbose
              cd - > /dev/null
            fi
      - save_cache:
          paths:
            - << parameters.path >>/node_modules
            - shared/node_modules
          key: v1-dependencies-{{ checksum "<< parameters.path >>/package-lock.json" }}
  
  setup_test_environment:
    description: "Setup test environment"
    steps:
      - run:
          name: Setup test environment
          command: |
            # Add TextEncoder polyfill for Node.js test environment
            echo "global.TextEncoder = require('util').TextEncoder;" >> setupTests.js
            echo "global.TextDecoder = require('util').TextDecoder;" >> setupTests.js
            echo "global.URL = require('url').URL;" >> setupTests.js
            echo "global.URLSearchParams = require('url').URLSearchParams;" >> setupTests.js
            # Setup test configs for all services
            node scripts/setup-test-configs.js

  run_tests:
    description: "Run tests"
    parameters:
      path:
        type: string
    steps:
      - run:
          name: Run tests
          command: |
            cd << parameters.path >>
            # Set test environment variables
            export NODE_ENV=test
            export TEST_MODE=true
            # Run tests with proper timeout
            npm test -- --testTimeout=30000 --forceExit --detectOpenHandles

jobs:
  test_auth_service:
    executor: node-executor
    steps:
      - install_deps:
          path: services/auth-service
      - setup_test_environment
      - run_tests:
          path: services/auth-service

  test_link_service:
    executor: node-executor
    steps:
      - install_deps:
          path: services/link-service
      - setup_test_environment
      - run_tests:
          path: services/link-service

  test_community_service:
    executor: node-executor
    steps:
      - install_deps:
          path: services/community-service
      - setup_test_environment
      - run_tests:
          path: services/community-service

  test_chat_service:
    executor: node-executor
    steps:
      - install_deps:
          path: services/chat-service
      - setup_test_environment
      - run_tests:
          path: services/chat-service

  test_news_service:
    executor: node-executor
    steps:
      - install_deps:
          path: services/news-service
      - setup_test_environment
      - run_tests:
          path: services/news-service

  test_admin_service:
    executor: node-executor
    steps:
      - install_deps:
          path: services/admin-service
      - setup_test_environment
      - run_tests:
          path: services/admin-service

  test_phishtank_service:
    executor: node-executor
    steps:
      - install_deps:
          path: services/phishtank-service
      - setup_test_environment
      - run_tests:
          path: services/phishtank-service

  test_api_gateway:
    executor: node-executor
    steps:
      - install_deps:
          path: services/api-gateway
      - setup_test_environment
      - run_tests:
          path: services/api-gateway

  test_frontend:
    executor: node-executor
    steps:
      - install_deps:
          path: client
      - setup_test_environment
      - run:
          name: Run frontend tests
          command: |
            cd client
            export NODE_ENV=test
            export CI=true
            # Add polyfills for test environment
            echo "global.TextEncoder = require('util').TextEncoder;" >> src/setupTests.js
            echo "global.TextDecoder = require('util').TextDecoder;" >> src/setupTests.js
            npm test -- --watchAll=false --testTimeout=30000

workflows:
  version: 2
  ci:
    jobs:
      - test_auth_service:
          context: factcheck-auth
      - test_link_service:
          context: factcheck-link
      - test_community_service:
          context: factcheck-community
      - test_chat_service:
          context: factcheck-chat
      - test_news_service:
          context: factcheck-news
      - test_admin_service:
          context: factcheck-admin
      - test_phishtank_service:
          context: factcheck-phishtank
      - test_api_gateway:
          context: factcheck-api-gateway
      - test_frontend:
          context: factcheck-frontend